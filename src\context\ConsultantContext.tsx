'use client';

import { useState, createContext, ReactNode, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

interface Appointment {
  _id: string;
  userId: string;
  docId: string;
  slotDate: string;
  slotTime: string;
  userData: {
    name: string;
    email: string;
    phone: string;
    address: {
      line1: string;
      line2: string;
    };
    gender: string;
    dob: string;
  };
  amount: number;
  date: number;
  cancelled: boolean;
  payment: boolean;
  isCompleted: boolean;
}

interface DashData {
  earnings: number;
  appointments: number;
  patients: number;
  latestAppointments: Appointment[];
}

interface ProfileData {
  _id: string;
  name: string;
  email: string;
  speciality: string;
  degree: string;
  experience: string;
  about: string;
  fees: number;
  address: {
    line1: string;
    line2: string;
  };
  available: boolean;
  image: string;
}

interface ConsultantContextType {
  dToken: string;
  setDToken: (token: string) => void;
  backendUrl: string;
  appointments: Appointment[];
  setAppointments: (appointments: Appointment[]) => void;
  getAppointments: () => Promise<void>;
  completeAppointment: (appointmentId: string) => Promise<void>;
  cancelAppointment: (appointmentId: string) => Promise<void>;
  dashData: DashData | false;
  setDashData: (data: DashData | false) => void;
  getDashData: () => Promise<void>;
  profileData: ProfileData | false;
  setProfileData: (data: ProfileData | false) => void;
  getProfileData: () => Promise<void>;
}

export const ConsultantContext = createContext<ConsultantContextType | null>(null);

interface ConsultantContextProviderProps {
  children: ReactNode;
}

const ConsultantContextProvider = ({ children }: ConsultantContextProviderProps) => {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || '';

  const [dToken, setDToken] = useState<string>('');
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [dashData, setDashData] = useState<DashData | false>(false);
  const [profileData, setProfileData] = useState<ProfileData | false>(false);

  // Initialize token from localStorage on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('dToken');
      if (token) {
        setDToken(token);
      }
    }
  }, []);

  const getAppointments = async () => {
    try {
      const { data } = await axios.get(
        backendUrl + '/api/consultant/appointments',
        {
          headers: {
            dToken,
          },
        }
      );
      if (data.success) {
        setAppointments(data.appointments);
        console.log(data.appointments);
      } else {
        toast.error(data.message);
      }
    } catch (error: any) {
      console.log(error);
      toast.error(error.message);
    }
  };

  const completeAppointment = async (appointmentId: string) => {
    try {
      const { data } = await axios.post(
        backendUrl + '/api/consultant/complete-appointment',
        { appointmentId },
        {
          headers: {
            dToken,
          },
        }
      );
      if (data.success) {
        toast.success(data.message);
        getAppointments();
      } else {
        toast.error(data.message);
      }
    } catch (error: any) {
      console.log(error);
      toast.error(error.message);
    }
  };

  const cancelAppointment = async (appointmentId: string) => {
    try {
      const { data } = await axios.post(
        backendUrl + '/api/consultant/cancel-appointment',
        { appointmentId },
        {
          headers: {
            dToken,
          },
        }
      );
      if (data.success) {
        toast.success(data.message);
        getAppointments();
      } else {
        toast.error(data.message);
      }
    } catch (error: any) {
      console.log(error);
      toast.error(error.message);
    }
  };

  const getDashData = async () => {
    try {
      const { data } = await axios.get(backendUrl + '/api/consultant/dashboard', {
        headers: {
          dToken,
        },
      });
      if (data.success) {
        setDashData(data.dashData);
        console.log(data.dashData);
      } else {
        toast.error(data.message);
      }
    } catch (error: any) {
      console.log(error);
      toast.error(error.message);
    }
  };

  const getProfileData = async () => {
    try {
      const { data } = await axios.get(backendUrl + '/api/consultant/profile', {
        headers: {
          dToken,
        },
      });
      if (data.success) {
        setProfileData(data.profileData);
        console.log(data.profileData);
      }
    } catch (error: any) {
      console.log(error);
      toast.error(error.message);
    }
  };

  const value: ConsultantContextType = {
    dToken,
    setDToken,
    backendUrl,
    appointments,
    setAppointments,
    getAppointments,
    completeAppointment,
    cancelAppointment,
    dashData,
    setDashData,
    getDashData,
    profileData,
    setProfileData,
    getProfileData,
  };

  return (
    <ConsultantContext.Provider value={value}>
      {children}
    </ConsultantContext.Provider>
  );
};

export default ConsultantContextProvider;
