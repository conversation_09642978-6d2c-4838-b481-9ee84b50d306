'use client';

import { ReactNode } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import AdminContextProvider from '@/context/AdminContext';
import ConsultantContextProvider from '@/context/ConsultantContext';
import AppContextProvider from '@/context/AppContext';

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <AdminContextProvider>
      <ConsultantContextProvider>
        <AppContextProvider>
          {children}
          <ToastContainer />
        </AppContextProvider>
      </ConsultantContextProvider>
    </AdminContextProvider>
  );
}
