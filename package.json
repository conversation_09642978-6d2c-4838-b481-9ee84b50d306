{"name": "admin", "private": true, "version": "0.0.0", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "5.2.1", "@radix-ui/react-checkbox": "1.3.3", "@radix-ui/react-dialog": "1.1.15", "@radix-ui/react-dropdown-menu": "2.1.16", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-navigation-menu": "1.2.14", "@radix-ui/react-popover": "1.1.15", "@radix-ui/react-select": "2.2.6", "@radix-ui/react-slot": "1.2.3", "axios": "^1.11.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "4.1.0", "framer-motion": "12.23.12", "lucide-react": "0.542.0", "next": "15.5.2", "next-themes": "0.4.6", "react": "19.1.0", "react-day-picker": "9.9.0", "react-dom": "19.1.0", "react-hook-form": "7.62.0", "react-icons": "5.5.0", "react-router-dom": "^7.8.2", "react-toastify": "^11.0.5", "tailwind-merge": "3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "1.3.8", "zod": "4.1.5"}, "devDependencies": {"@tailwindcss/postcss": "4", "@types/node": "^20", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "autoprefixer": "10.4.21", "eslint": "9.33.0", "eslint-config-next": "15.5.2", "postcss": "8.5.6", "prettier": "3.6.2", "tailwindcss": "4.1.13", "typescript": "^5"}}