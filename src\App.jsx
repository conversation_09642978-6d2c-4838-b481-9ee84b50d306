import React from 'react';
import Login from './pages/Login';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useContext } from 'react';
import { AdminContext } from './context/AdminContext';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import { Route, Routes } from 'react-router-dom';
import Dashboard from './pages/Admin/Dashboard';
import AllAppointments from './pages/Admin/AllAppointments';
import AddConsultant from './pages/Admin/AddConsultant';
import ConsultantsList from './pages/Admin/ConsultantsList';
import { ConsultantContext } from './context/ConsultantContext';
import ConsultantDashboard from './pages/Consultant/ConsultantDashboard';
import ConsultantAppointments from './pages/Consultant/ConsultantAppointments';
import ConsultantProfile from './pages/Consultant/ConsultantProfile';

const App = () => {
  const { aToken } = useContext(AdminContext);
  const { dToken } = useContext(ConsultantContext);

  return aToken || dToken ? (
    <div className="bg-[#F8F9FD]">
      <ToastContainer />
      <Navbar />
      <div className="flex items-start">
        <Sidebar />
        <Routes>
          {/* Admin Route */}
          <Route path="/" element={<></>}></Route>
          <Route path="/admin-dashboard" element={<Dashboard />}></Route>
          <Route path="/all-appointments" element={<AllAppointments />}></Route>
          <Route path="/add-consultant" element={<AddConsultant />}></Route>
          <Route path="/consultant-list" element={<ConsultantsList />}></Route>

          {/* Consultant Route */}
          <Route path="/consultant-dashboard" element={<ConsultantDashboard />}></Route>
          <Route path="/consultant-appointments" element={<ConsultantAppointments />}></Route>
          <Route path="/consultant-profile" element={<ConsultantProfile />}></Route>
        </Routes>
      </div>
    </div>
  ) : (
    <>
      <Login />
      <ToastContainer />
    </>
  );
};

export default App;
