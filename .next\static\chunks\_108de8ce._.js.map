{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useContext, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { AdminContext } from '@/context/AdminContext';\nimport { ConsultantContext } from '@/context/ConsultantContext';\n\nexport default function HomePage() {\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!adminContext || !consultantContext) return;\n\n    const { aToken } = adminContext;\n    const { dToken } = consultantContext;\n\n    if (aToken) {\n      router.push('/admin-dashboard');\n    } else if (dToken) {\n      router.push('/consultant-dashboard');\n    } else {\n      router.push('/login');\n    }\n  }, [adminContext, consultantContext, router]);\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,eAAe,IAAA,2KAAU,EAAC,kJAAY;IAC5C,MAAM,oBAAoB,IAAA,2KAAU,EAAC,4JAAiB;IACtD,MAAM,SAAS,IAAA,kJAAS;IAExB,IAAA,0KAAS;8BAAC;YACR,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;YAEzC,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,MAAM,EAAE,MAAM,EAAE,GAAG;YAEnB,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,QAAQ;gBACjB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAc;QAAmB;KAAO;IAE5C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;GAzBwB;;QAGP,kJAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}