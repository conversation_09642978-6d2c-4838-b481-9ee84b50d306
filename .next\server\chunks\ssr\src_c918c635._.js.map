{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline:\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary:\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : 'button';\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,mNAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,mNAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,IAAA,yHAAE,EACX,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst labelVariants = cva(\n  'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,IAAA,uKAAG,EACvB;AAGF,MAAM,sBAAQ,mNAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,yKAAmB;QAClB,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,yKAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext, useState } from 'react';\nimport { assets } from '@/assets/assets';\nimport { AdminContext } from '@/context/AdminContext';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport { ConsultantContext } from '@/context/ConsultantContext';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\n\nconst LoginPage: React.FC = () => {\n  const [state, setState] = useState<'Admin' | 'Consultant'>('Admin');\n  const [email, setEmail] = useState<string>('');\n  const [password, setPassword] = useState<string>('');\n\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const router = useRouter();\n\n  if (!adminContext || !consultantContext) {\n    return <div>Loading...</div>;\n  }\n\n  const { setAToken, backendUrl } = adminContext;\n  const { setDToken } = consultantContext;\n\n  const onSubmitHandler = async (event: React.FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    \n    // Development bypass - REMOVE IN PRODUCTION\n    if (email === '<EMAIL>' && password === 'dev123') {\n      const mockToken = 'dev-admin-token';\n      localStorage.setItem('aToken', mockToken);\n      setAToken(mockToken);\n      router.push('/admin-dashboard');\n      return;\n    }\n\n    // Original API calls...\n    try {\n      if (state === 'Admin') {\n        const { data } = await axios.post(backendUrl + '/api/admin/login', {\n          email,\n          password,\n        });\n        if (data.success) {\n          localStorage.setItem('aToken', data.token);\n          setAToken(data.token);\n          router.push('/admin-dashboard');\n        } else {\n          toast.error(data.message);\n        }\n      } else {\n        const { data } = await axios.post(backendUrl + '/api/consultant/login', {\n          email,\n          password,\n        });\n        if (data.success) {\n          localStorage.setItem('dToken', data.token);\n          setDToken(data.token);\n          console.log(data.token);\n          router.push('/consultant-dashboard');\n        } else {\n          toast.error(data.message);\n        }\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      toast.error('Login failed. Please try again.');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <form onSubmit={onSubmitHandler} className=\"min-h-[80vh] flex items-center\">\n        <div className=\"flex flex-col gap-3 m-auto items-start p-8 min-w-[340px] sm:min-w-96 border rounded-xl text-[#5E5E5E] text-sm shadow-lg bg-white\">\n          <p className=\"text-2xl font-semibold m-auto\">\n            <span className=\"text-primary\">{state}</span> Login\n          </p>\n          <div className=\"w-full\">\n            <Label htmlFor=\"email\">Email</Label>\n            <Input\n              id=\"email\"\n              onChange={(e) => setEmail(e.target.value)}\n              value={email}\n              className=\"mt-1\"\n              type=\"email\"\n              required\n            />\n          </div>\n          <div className=\"w-full\">\n            <Label htmlFor=\"password\">Password</Label>\n            <Input\n              id=\"password\"\n              onChange={(e) => setPassword(e.target.value)}\n              value={password}\n              className=\"mt-1\"\n              type=\"password\"\n              required\n            />\n          </div>\n          <Button type=\"submit\" className=\"w-full\">\n            Login\n          </Button>\n          {state === 'Admin' ? (\n            <p>\n              Consultant Login?{' '}\n              <span\n                className=\"text-primary underline cursor-pointer\"\n                onClick={() => setState('Consultant')}\n              >\n                click here\n              </span>\n            </p>\n          ) : (\n            <p>\n              Admin Login?{' '}\n              <span\n                className=\"text-primary underline cursor-pointer\"\n                onClick={() => setState('Admin')}\n              >\n                click here\n              </span>\n            </p>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAaA,MAAM,YAAsB;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAyB;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAS;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAS;IAEjD,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,oBAAoB,IAAA,mNAAU,EAAC,yJAAiB;IACtD,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QACvC,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG;IAClC,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,MAAM,kBAAkB,OAAO;QAC7B,MAAM,cAAc;QAEpB,4CAA4C;QAC5C,IAAI,UAAU,mBAAmB,aAAa,UAAU;YACtD,MAAM,YAAY;YAClB,aAAa,OAAO,CAAC,UAAU;YAC/B,UAAU;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,wBAAwB;QACxB,IAAI;YACF,IAAI,UAAU,SAAS;gBACrB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,IAAI,CAAC,aAAa,oBAAoB;oBACjE;oBACA;gBACF;gBACA,IAAI,KAAK,OAAO,EAAE;oBAChB,aAAa,OAAO,CAAC,UAAU,KAAK,KAAK;oBACzC,UAAU,KAAK,KAAK;oBACpB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;gBAC1B;YACF,OAAO;gBACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,IAAI,CAAC,aAAa,yBAAyB;oBACtE;oBACA;gBACF;gBACA,IAAI,KAAK,OAAO,EAAE;oBAChB,aAAa,OAAO,CAAC,UAAU,KAAK,KAAK;oBACzC,UAAU,KAAK,KAAK;oBACpB,QAAQ,GAAG,CAAC,KAAK,KAAK;oBACtB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,4JAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAK,UAAU;YAAiB,WAAU;sBACzC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;4BAAa;;;;;;;kCAE/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0IAAK;gCAAC,SAAQ;0CAAQ;;;;;;0CACvB,8OAAC,0IAAK;gCACJ,IAAG;gCACH,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,OAAO;gCACP,WAAU;gCACV,MAAK;gCACL,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0IAAK;gCAAC,SAAQ;0CAAW;;;;;;0CAC1B,8OAAC,0IAAK;gCACJ,IAAG;gCACH,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,OAAO;gCACP,WAAU;gCACV,MAAK;gCACL,QAAQ;;;;;;;;;;;;kCAGZ,8OAAC,4IAAM;wBAAC,MAAK;wBAAS,WAAU;kCAAS;;;;;;oBAGxC,UAAU,wBACT,8OAAC;;4BAAE;4BACiB;0CAClB,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,SAAS;0CACzB;;;;;;;;;;;iFAKH,8OAAC;;4BAAE;4BACY;0CACb,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,SAAS;0CACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}