'use client';

import { useContext, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminContext } from '@/context/AdminContext';
import { ConsultantContext } from '@/context/ConsultantContext';

export default function HomePage() {
  const adminContext = useContext(AdminContext);
  const consultantContext = useContext(ConsultantContext);
  const router = useRouter();

  useEffect(() => {
    if (!adminContext || !consultantContext) return;

    const { aToken } = adminContext;
    const { dToken } = consultantContext;

    if (aToken) {
      router.push('/admin-dashboard');
    } else if (dToken) {
      router.push('/consultant-dashboard');
    } else {
      router.push('/login');
    }
  }, [adminContext, consultantContext, router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
    </div>
  );
}
