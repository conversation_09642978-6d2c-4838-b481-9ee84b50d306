'use client';

import React, { useContext, useState } from 'react';
import { assets } from '@/assets/assets';
import { AdminContext } from '@/context/AdminContext';
import axios from 'axios';
import { toast } from 'react-toastify';
import { ConsultantContext } from '@/context/ConsultantContext';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const LoginPage: React.FC = () => {
  const [state, setState] = useState<'Admin' | 'Consultant'>('Admin');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');

  const adminContext = useContext(AdminContext);
  const consultantContext = useContext(ConsultantContext);
  const router = useRouter();

  if (!adminContext || !consultantContext) {
    return <div>Loading...</div>;
  }

  const { setAToken, backendUrl } = adminContext;
  const { setDToken } = consultantContext;

  const onSubmitHandler = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    // Development bypass - REMOVE IN PRODUCTION
    if (email === '<EMAIL>' && password === 'dev123') {
      const mockToken = 'dev-admin-token';
      localStorage.setItem('aToken', mockToken);
      setAToken(mockToken);
      router.push('/admin-dashboard');
      return;
    }

    // Original API calls...
    try {
      if (state === 'Admin') {
        const { data } = await axios.post(backendUrl + '/api/admin/login', {
          email,
          password,
        });
        if (data.success) {
          localStorage.setItem('aToken', data.token);
          setAToken(data.token);
          router.push('/admin-dashboard');
        } else {
          toast.error(data.message);
        }
      } else {
        const { data } = await axios.post(backendUrl + '/api/consultant/login', {
          email,
          password,
        });
        if (data.success) {
          localStorage.setItem('dToken', data.token);
          setDToken(data.token);
          console.log(data.token);
          router.push('/consultant-dashboard');
        } else {
          toast.error(data.message);
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <form onSubmit={onSubmitHandler} className="min-h-[80vh] flex items-center">
        <div className="flex flex-col gap-3 m-auto items-start p-8 min-w-[340px] sm:min-w-96 border rounded-xl text-[#5E5E5E] text-sm shadow-lg bg-white">
          <p className="text-2xl font-semibold m-auto">
            <span className="text-primary">{state}</span> Login
          </p>
          <div className="w-full">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              onChange={(e) => setEmail(e.target.value)}
              value={email}
              className="mt-1"
              type="email"
              required
            />
          </div>
          <div className="w-full">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              onChange={(e) => setPassword(e.target.value)}
              value={password}
              className="mt-1"
              type="password"
              required
            />
          </div>
          <Button type="submit" className="w-full">
            Login
          </Button>
          {state === 'Admin' ? (
            <p>
              Consultant Login?{' '}
              <span
                className="text-primary underline cursor-pointer"
                onClick={() => setState('Consultant')}
              >
                click here
              </span>
            </p>
          ) : (
            <p>
              Admin Login?{' '}
              <span
                className="text-primary underline cursor-pointer"
                onClick={() => setState('Admin')}
              >
                click here
              </span>
            </p>
          )}
        </div>
      </form>
    </div>
  );
};

export default LoginPage;
