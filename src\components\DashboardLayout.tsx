'use client';

import React, { useContext, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminContext } from '@/context/AdminContext';
import { ConsultantContext } from '@/context/ConsultantContext';
import Navbar from '@/components/Navbar';
import Sidebar from '@/components/Sidebar';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const adminContext = useContext(AdminContext);
  const consultantContext = useContext(ConsultantContext);
  const router = useRouter();

  useEffect(() => {
    if (!adminContext || !consultantContext) return;

    const { aToken } = adminContext;
    const { dToken } = consultantContext;

    if (!aToken && !dToken) {
      router.push('/login');
    }
  }, [admin<PERSON><PERSON><PERSON><PERSON>, consultantContext, router]);

  if (!adminContext || !consultantContext) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  const { aToken } = adminContext;
  const { dToken } = consultantContext;

  if (!aToken && !dToken) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="bg-[#F8F9FD]">
      <Navbar />
      <div className="flex items-start">
        <Sidebar />
        <div className="flex-1 p-4">
          {children}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
