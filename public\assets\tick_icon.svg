<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_6081_341)">
<circle cx="25" cy="21" r="21" fill="#F5FEF7"/>
<circle cx="25" cy="21" r="20.5" stroke="#7CFF89"/>
</g>
<rect x="23.518" y="25.7374" width="11.478" height="0.732635" rx="0.366318" transform="rotate(-45.7402 23.518 25.7374)" fill="#45FF57" stroke="#52FF64" stroke-width="0.732635"/>
<rect x="23.6144" y="26.0328" width="5.71838" height="0.732635" rx="0.366318" transform="rotate(-135.74 23.6144 26.0328)" fill="#45FF57" stroke="#52FF64" stroke-width="0.732635"/>
<defs>
<filter id="filter0_d_6081_341" x="0" y="0" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.972549 0 0 0 0 0.745098 0 0 0 0 0.701961 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6081_341"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6081_341" result="shape"/>
</filter>
</defs>
</svg>
