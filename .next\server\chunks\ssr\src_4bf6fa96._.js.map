{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/assets/assets.ts"], "sourcesContent": ["export const assets = {\n  add_icon: '/assets/add_icon.svg',\n  admin_logo: '/assets/admin_logo.svg',\n  appointment_icon: '/assets/appointment_icon.svg',\n  cancel_icon: '/assets/cancel_icon.svg',\n  doctor_icon: '/assets/doctor_icon.svg',\n  upload_area: '/assets/upload_area.svg',\n  home_icon: '/assets/home_icon.svg',\n  patients_icon: '/assets/patients_icon.svg',\n  people_icon: '/assets/people_icon.svg',\n  list_icon: '/assets/list_icon.svg',\n  tick_icon: '/assets/tick_icon.svg',\n  appointments_icon: '/assets/appointments_icon.svg',\n  earning_icon: '/assets/earning_icon.svg',\n};\n"], "names": [], "mappings": ";;;;AAAO,MAAM,SAAS;IACpB,UAAU;IACV,YAAY;IACZ,kBAAkB;IAClB,aAAa;IACb,aAAa;IACb,aAAa;IACb,WAAW;IACX,eAAe;IACf,aAAa;IACb,WAAW;IACX,WAAW;IACX,mBAAmB;IACnB,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { assets } from '@/assets/assets';\nimport { AdminContext } from '@/context/AdminContext';\nimport { useRouter } from 'next/navigation';\nimport { ConsultantContext } from '@/context/ConsultantContext';\n\nconst Navbar: React.FC = () => {\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const router = useRouter();\n\n  if (!adminContext || !consultantContext) {\n    return null;\n  }\n\n  const { aToken, setAToken } = adminContext;\n  const { dToken, setDToken } = consultantContext;\n\n  const logout = () => {\n    router.push('/');\n    if (aToken) {\n      setAToken('');\n      localStorage.removeItem('aToken');\n    }\n    if (dToken) {\n      setDToken('');\n      localStorage.removeItem('dToken');\n    }\n  };\n\n  return (\n    <div className=\"flex justify-between items-center px-4 sm:px-10 py-3 border-b bg-white\">\n      <div className=\"flex items-center gap-2 text-xs\">\n        <img\n          className=\"w-36 sm:w-40 cursor-pointer\"\n          src={assets.admin_logo}\n          alt=\"Admin Logo\"\n        />\n        <p className=\"border px-2.5 py-0.5 rounded-full border-gray-500 text-gray-600\">\n          {aToken ? 'Admin' : 'Consultant'}\n        </p>\n      </div>\n      <button\n        onClick={logout}\n        className=\"bg-primary text-white text-sm px-10 py-2 rounded-full\"\n      >\n        Logout\n      </button>\n    </div>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,SAAmB;IACvB,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,oBAAoB,IAAA,mNAAU,EAAC,yJAAiB;IACtD,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QACvC,OAAO;IACT;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAC9B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IAE9B,MAAM,SAAS;QACb,OAAO,IAAI,CAAC;QACZ,IAAI,QAAQ;YACV,UAAU;YACV,aAAa,UAAU,CAAC;QAC1B;QACA,IAAI,QAAQ;YACV,UAAU;YACV,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,KAAK,iIAAM,CAAC,UAAU;wBACtB,KAAI;;;;;;kCAEN,8OAAC;wBAAE,WAAU;kCACV,SAAS,UAAU;;;;;;;;;;;;0BAGxB,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP;uCAEe", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { AdminContext } from '@/context/AdminContext';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { assets } from '@/assets/assets';\nimport { ConsultantContext } from '@/context/ConsultantContext';\n\nconst Sidebar: React.FC = () => {\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const pathname = usePathname();\n\n  if (!adminContext || !consultantContext) {\n    return null;\n  }\n\n  const { aToken } = adminContext;\n  const { dToken } = consultantContext;\n\n  const isActive = (path: string) => pathname === path;\n\n  const linkClass = (path: string) =>\n    `flex items-center gap-3 py-3.5 px-3 md:px-9 md:min-w-72 cursor-pointer ${\n      isActive(path) ? 'bg-[#F2F3FF] border-r-4 border-primary' : ''\n    }`;\n\n  return (\n    <div className=\"min-h-screen bg-white border-r\">\n      {aToken && (\n        <ul className=\"text-[#515151] mt-5\">\n          <Link href=\"/admin-dashboard\" className={linkClass('/admin-dashboard')}>\n            <img src={assets.home_icon} alt=\"Dashboard\" />\n            <p className=\"hidden md:block\">Dashboard</p>\n          </Link>\n          <Link href=\"/all-appointments\" className={linkClass('/all-appointments')}>\n            <img src={assets.appointment_icon} alt=\"Appointments\" />\n            <p className=\"hidden md:block\">Appointments</p>\n          </Link>\n          <Link href=\"/add-consultant\" className={linkClass('/add-consultant')}>\n            <img src={assets.add_icon} alt=\"Add Consultant\" />\n            <p className=\"hidden md:block\">Add Consultant</p>\n          </Link>\n          <Link href=\"/consultant-list\" className={linkClass('/consultant-list')}>\n            <img src={assets.people_icon} alt=\"Consultant List\" />\n            <p>Consultant List</p>\n          </Link>\n        </ul>\n      )}\n      {dToken && (\n        <ul className=\"text-[#515151] mt-5\">\n          <Link href=\"/consultant-dashboard\" className={linkClass('/consultant-dashboard')}>\n            <img src={assets.home_icon} alt=\"Dashboard\" />\n            <p className=\"hidden md:block\">Dashboard</p>\n          </Link>\n          <Link href=\"/consultant-appointments\" className={linkClass('/consultant-appointments')}>\n            <img src={assets.appointment_icon} alt=\"Appointments\" />\n            <p className=\"hidden md:block\">Appointments</p>\n          </Link>\n          <Link href=\"/consultant-profile\" className={linkClass('/consultant-profile')}>\n            <img src={assets.people_icon} alt=\"Profile\" />\n            <p className=\"hidden md:block\">Profile</p>\n          </Link>\n        </ul>\n      )}\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,UAAoB;IACxB,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,oBAAoB,IAAA,mNAAU,EAAC,yJAAiB;IACtD,MAAM,WAAW,IAAA,iJAAW;IAE5B,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QACvC,OAAO;IACT;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,WAAW,CAAC,OAAiB,aAAa;IAEhD,MAAM,YAAY,CAAC,OACjB,CAAC,uEAAuE,EACtE,SAAS,QAAQ,2CAA2C,IAC5D;IAEJ,qBACE,8OAAC;QAAI,WAAU;;YACZ,wBACC,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC,uKAAI;wBAAC,MAAK;wBAAmB,WAAW,UAAU;;0CACjD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,SAAS;gCAAE,KAAI;;;;;;0CAChC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAoB,WAAW,UAAU;;0CAClD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,gBAAgB;gCAAE,KAAI;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAkB,WAAW,UAAU;;0CAChD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,QAAQ;gCAAE,KAAI;;;;;;0CAC/B,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAmB,WAAW,UAAU;;0CACjD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,WAAW;gCAAE,KAAI;;;;;;0CAClC,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;YAIR,wBACC,8OAAC;gBAAG,WAAU;;kCACZ,8OAAC,uKAAI;wBAAC,MAAK;wBAAwB,WAAW,UAAU;;0CACtD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,SAAS;gCAAE,KAAI;;;;;;0CAChC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAA2B,WAAW,UAAU;;0CACzD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,gBAAgB;gCAAE,KAAI;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;kCAEjC,8OAAC,uKAAI;wBAAC,MAAK;wBAAsB,WAAW,UAAU;;0CACpD,8OAAC;gCAAI,KAAK,iIAAM,CAAC,WAAW;gCAAE,KAAI;;;;;;0CAClC,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;uCAEe", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { AdminContext } from '@/context/AdminContext';\nimport { ConsultantContext } from '@/context/ConsultantContext';\nimport Navbar from '@/components/Navbar';\nimport Sidebar from '@/components/Sidebar';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {\n  const adminContext = useContext(AdminContext);\n  const consultantContext = useContext(ConsultantContext);\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!adminContext || !consultantContext) return;\n\n    const { aToken } = adminContext;\n    const { dToken } = consultantContext;\n\n    if (!aToken && !dToken) {\n      router.push('/login');\n    }\n  }, [admin<PERSON><PERSON><PERSON><PERSON>, consultantContext, router]);\n\n  if (!adminContext || !consultantContext) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  const { aToken } = adminContext;\n  const { dToken } = consultantContext;\n\n  if (!aToken && !dToken) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-[#F8F9FD]\">\n      <Navbar />\n      <div className=\"flex items-start\">\n        <Sidebar />\n        <div className=\"flex-1 p-4\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,oBAAoB,IAAA,mNAAU,EAAC,yJAAiB;IACtD,MAAM,SAAS,IAAA,+IAAS;IAExB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QAEzC,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,IAAI,CAAC,UAAU,CAAC,QAAQ;YACtB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAc;QAAmB;KAAO;IAE5C,IAAI,CAAC,gBAAgB,CAAC,mBAAmB;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wIAAO;;;;;kCACR,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/app/admin-dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useContext } from 'react';\nimport { AdminContext } from '@/context/AdminContext';\nimport { assets } from '@/assets/assets';\nimport { AppContext } from '@/context/AppContext';\nimport DashboardLayout from '@/components/DashboardLayout';\n\nconst AdminDashboard: React.FC = () => {\n  const adminContext = useContext(AdminContext);\n  const appContext = useContext(AppContext);\n\n  if (!adminContext || !appContext) {\n    return <div>Loading...</div>;\n  }\n\n  const { aToken, getDashData, cancelAppointment, dashData } = adminContext;\n  const { slotDateFormat } = appContext;\n\n  useEffect(() => {\n    if (aToken) {\n      getDashData();\n    }\n  }, [aToken, getDashData]);\n\n  if (!dashData) {\n    return (\n      <DashboardLayout>\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"m-5\">\n        <div className=\"flex flex-wrap gap-3\">\n          <div className=\"flex items-center gap-2 bg-white p-4 min-w-52 rounded border-2 border-gray-100 cursor-pointer hover:scale-105 transition-all\">\n            <img className=\"w-14\" src={assets.doctor_icon} alt=\"Consultants\" />\n            <div>\n              <p className=\"text-xl font-semibold text-gray-600\">\n                {dashData.consultants}\n              </p>\n              <p className=\"text-gray-400\">Consultants</p>\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2 bg-white p-4 min-w-52 rounded border-2 border-gray-100 cursor-pointer hover:scale-105 transition-all\">\n            <img className=\"w-14\" src={assets.appointments_icon} alt=\"Appointments\" />\n            <div>\n              <p className=\"text-xl font-semibold text-gray-600\">\n                {dashData.appointments}\n              </p>\n              <p className=\"text-gray-400\">Appointments</p>\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2 bg-white p-4 min-w-52 rounded border-2 border-gray-100 cursor-pointer hover:scale-105 transition-all\">\n            <img className=\"w-14\" src={assets.patients_icon} alt=\"Patients\" />\n            <div>\n              <p className=\"text-xl font-semibold text-gray-600\">\n                {dashData.patients}\n              </p>\n              <p className=\"text-gray-400\">Patients</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white\">\n          <div className=\"flex items-center gap-2.5 px-4 py-4 mt-10 rounded-t border\">\n            <img src={assets.list_icon} alt=\"List\" />\n            <p className=\"font-semibold\">Latest Bookings</p>\n          </div>\n          <div className=\"pt-4 border border-t-0\">\n            {(dashData.latestAppointments || []).map((item, index) => (\n              <div\n                className=\"flex items-center px-6 py-3 gap-3 hover:bg-gray-100\"\n                key={index}\n              >\n                <img\n                  className=\"rounded-full w-10\"\n                  src={item.docData.image}\n                  alt={item.docData.name}\n                />\n                <div className=\"flex-1 text-sm\">\n                  <p className=\"text-gray-800 font-medium\">\n                    {item.docData.name}\n                  </p>\n                  <p className=\"text-gray-600\">\n                    {slotDateFormat(item.slotDate)}\n                  </p>\n                </div>\n                {item.cancelled ? (\n                  <p className=\"text-red-400 text-xs font-medium\">Cancelled</p>\n                ) : item.isCompleted ? (\n                  <p className=\"text-green-500 text-xs font-medium\">\n                    Completed\n                  </p>\n                ) : (\n                  <img\n                    onClick={() => cancelAppointment(item._id)}\n                    className=\"w-10 cursor-pointer\"\n                    src={assets.cancel_icon}\n                    alt=\"Cancel\"\n                  />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,iBAA2B;IAC/B,MAAM,eAAe,IAAA,mNAAU,EAAC,+IAAY;IAC5C,MAAM,aAAa,IAAA,mNAAU,EAAC,2IAAU;IAExC,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAChC,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,GAAG;IAC7D,MAAM,EAAE,cAAc,EAAE,GAAG;IAE3B,IAAA,kNAAS,EAAC;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC,gJAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC,gJAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAO,KAAK,iIAAM,CAAC,WAAW;oCAAE,KAAI;;;;;;8CACnD,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAEvB,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAGjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAO,KAAK,iIAAM,CAAC,iBAAiB;oCAAE,KAAI;;;;;;8CACzD,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDACV,SAAS,YAAY;;;;;;sDAExB,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAGjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAO,KAAK,iIAAM,CAAC,aAAa;oCAAE,KAAI;;;;;;8CACrD,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDACV,SAAS,QAAQ;;;;;;sDAEpB,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;8BAInC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,KAAK,iIAAM,CAAC,SAAS;oCAAE,KAAI;;;;;;8CAChC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;sCACZ,CAAC,SAAS,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,sBAC9C,8OAAC;oCACC,WAAU;;sDAGV,8OAAC;4CACC,WAAU;4CACV,KAAK,KAAK,OAAO,CAAC,KAAK;4CACvB,KAAK,KAAK,OAAO,CAAC,IAAI;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,KAAK,OAAO,CAAC,IAAI;;;;;;8DAEpB,8OAAC;oDAAE,WAAU;8DACV,eAAe,KAAK,QAAQ;;;;;;;;;;;;wCAGhC,KAAK,SAAS,iBACb,8OAAC;4CAAE,WAAU;sDAAmC;;;;;uFAC9C,KAAK,WAAW,iBAClB,8OAAC;4CAAE,WAAU;sDAAqC;;;;;qGAIlD,8OAAC;4CACC,SAAS,IAAM,kBAAkB,KAAK,GAAG;4CACzC,WAAU;4CACV,KAAK,iIAAM,CAAC,WAAW;4CACvB,KAAI;;;;;;;mCA1BH;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCrB;uCAEe", "debugId": null}}]}