{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/theme-provider.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n  attribute?: 'class' | 'data-theme' | string;\n  defaultTheme?: string;\n  enableSystem?: boolean;\n  disableTransitionOnChange?: boolean;\n}\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAaO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,iKAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/context/AdminContext.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, createContext, useContext, ReactNode, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\n\ninterface Consultant {\n  _id: string;\n  name: string;\n  email: string;\n  speciality: string;\n  degree: string;\n  experience: string;\n  about: string;\n  fees: number;\n  address: {\n    line1: string;\n    line2: string;\n  };\n  available: boolean;\n  image: string;\n  date: number;\n}\n\ninterface Appointment {\n  _id: string;\n  userId: string;\n  docId: string;\n  slotDate: string;\n  slotTime: string;\n  userData: {\n    name: string;\n    email: string;\n    phone: string;\n    address: {\n      line1: string;\n      line2: string;\n    };\n    gender: string;\n    dob: string;\n  };\n  docData: Consultant;\n  amount: number;\n  date: number;\n  cancelled: boolean;\n  payment: boolean;\n  isCompleted: boolean;\n}\n\ninterface DashData {\n  consultants: number;\n  appointments: number;\n  patients: number;\n  latestAppointments: Appointment[];\n}\n\ninterface AdminContextType {\n  aToken: string;\n  setAToken: (token: string) => void;\n  backendUrl: string;\n  consultants: Consultant[];\n  getAllConsultants: () => Promise<void>;\n  changeAvailability: (docId: string) => Promise<void>;\n  appointments: Appointment[];\n  setAppointments: (appointments: Appointment[]) => void;\n  getAllAppointments: () => Promise<void>;\n  cancelAppointment: (appointmentId: string) => Promise<void>;\n  dashData: DashData | false;\n  getDashData: () => Promise<void>;\n}\n\nexport const AdminContext = createContext<AdminContextType | null>(null);\n\ninterface AdminContextProviderProps {\n  children: ReactNode;\n}\n\nconst AdminContextProvider = ({ children }: AdminContextProviderProps) => {\n  const [aToken, setAToken] = useState<string>('');\n  const [consultants, setConsultants] = useState<Consultant[]>([]);\n  const [appointments, setAppointments] = useState<Appointment[]>([]);\n  const [dashData, setDashData] = useState<DashData | false>(false);\n\n  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || '';\n\n  // Initialize token from localStorage on client side\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('aToken');\n      if (token) {\n        setAToken(token);\n      }\n    }\n  }, []);\n\n  const getAllConsultants = async () => {\n    try {\n      const { data } = await axios.post(\n        backendUrl + '/api/admin/all-consultants',\n        {},\n        {\n          headers: {\n            aToken,\n          },\n        }\n      );\n      if (data.success) {\n        setConsultants(data.consultants);\n        console.log(data.consultants);\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      toast.error(error.message);\n    }\n  };\n\n  const cancelAppointment = async (appointmentId: string) => {\n    try {\n      const { data } = await axios.post(\n        backendUrl + '/api/admin/cancel-appointment',\n        { appointmentId },\n        {\n          headers: {\n            aToken,\n          },\n        }\n      );\n\n      if (data.success) {\n        toast.success(data.message);\n        getAllAppointments();\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      toast.error(error.message);\n    }\n  };\n\n  const changeAvailability = async (docId: string) => {\n    try {\n      const { data } = await axios.post(\n        backendUrl + '/api/admin/change-availability',\n        { docId },\n        {\n          headers: {\n            aToken,\n          },\n        }\n      );\n      if (data.success) {\n        toast.success(data.message);\n        getAllConsultants();\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      toast.error(error.message);\n    }\n  };\n\n  const getAllAppointments = async () => {\n    try {\n      const { data } = await axios.get(backendUrl + '/api/admin/appointments', {\n        headers: {\n          aToken,\n        },\n      });\n\n      if (data.success) {\n        setAppointments(data.appointments);\n        console.log(data.appointments);\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      toast.error(error.message);\n    }\n  };\n\n  const getDashData = async () => {\n    try {\n      const { data } = await axios.get(backendUrl + '/api/admin/dashboard', {\n        headers: {\n          aToken,\n        },\n      });\n\n      if (data.success) {\n        setDashData(data.dashData);\n        console.log(data.dashData);\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      toast.error(error.message);\n    }\n  };\n\n  const value: AdminContextType = {\n    aToken,\n    setAToken,\n    backendUrl,\n    consultants,\n    getAllConsultants,\n    changeAvailability,\n    appointments,\n    setAppointments,\n    getAllAppointments,\n    cancelAppointment,\n    dashData,\n    getDashData,\n  };\n\n  return (\n    <AdminContext.Provider value={value}>\n      {children}\n    </AdminContext.Provider>\n  );\n};\n\nexport default AdminContextProvider;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAuEO,MAAM,6BAAe,IAAA,sNAAa,EAA0B;AAMnE,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAA6B;IACnE,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAS;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAe,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAgB,EAAE;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAmB;IAE3D,MAAM,aAAa,QAAQ,GAAG,CAAC,uBAAuB,IAAI;IAE1D,oDAAoD;IACpD,IAAA,kNAAS,EAAC;QACR;;IAMF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,IAAI,CAC/B,aAAa,8BACb,CAAC,GACD;gBACE,SAAS;oBACP;gBACF;YACF;YAEF,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe,KAAK,WAAW;gBAC/B,QAAQ,GAAG,CAAC,KAAK,WAAW;YAC9B,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,IAAI,CAC/B,aAAa,iCACb;gBAAE;YAAc,GAChB;gBACE,SAAS;oBACP;gBACF;YACF;YAGF,IAAI,KAAK,OAAO,EAAE;gBAChB,4JAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B;YACF,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,IAAI,CAC/B,aAAa,kCACb;gBAAE;YAAM,GACR;gBACE,SAAS;oBACP;gBACF;YACF;YAEF,IAAI,KAAK,OAAO,EAAE;gBAChB,4JAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B;YACF,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,GAAG,CAAC,aAAa,2BAA2B;gBACvE,SAAS;oBACP;gBACF;YACF;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,YAAY;gBACjC,QAAQ,GAAG,CAAC,KAAK,YAAY;YAC/B,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,GAAG,CAAC,aAAa,wBAAwB;gBACpE,SAAS;oBACP;gBACF;YACF;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,QAAQ;gBACzB,QAAQ,GAAG,CAAC,KAAK,QAAQ;YAC3B,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/context/ConsultantContext.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, createContext, ReactNode, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\n\ninterface Appointment {\n  _id: string;\n  userId: string;\n  docId: string;\n  slotDate: string;\n  slotTime: string;\n  userData: {\n    name: string;\n    email: string;\n    phone: string;\n    address: {\n      line1: string;\n      line2: string;\n    };\n    gender: string;\n    dob: string;\n  };\n  amount: number;\n  date: number;\n  cancelled: boolean;\n  payment: boolean;\n  isCompleted: boolean;\n}\n\ninterface DashData {\n  earnings: number;\n  appointments: number;\n  patients: number;\n  latestAppointments: Appointment[];\n}\n\ninterface ProfileData {\n  _id: string;\n  name: string;\n  email: string;\n  speciality: string;\n  degree: string;\n  experience: string;\n  about: string;\n  fees: number;\n  address: {\n    line1: string;\n    line2: string;\n  };\n  available: boolean;\n  image: string;\n}\n\ninterface ConsultantContextType {\n  dToken: string;\n  setDToken: (token: string) => void;\n  backendUrl: string;\n  appointments: Appointment[];\n  setAppointments: (appointments: Appointment[]) => void;\n  getAppointments: () => Promise<void>;\n  completeAppointment: (appointmentId: string) => Promise<void>;\n  cancelAppointment: (appointmentId: string) => Promise<void>;\n  dashData: DashData | false;\n  setDashData: (data: DashData | false) => void;\n  getDashData: () => Promise<void>;\n  profileData: ProfileData | false;\n  setProfileData: (data: ProfileData | false) => void;\n  getProfileData: () => Promise<void>;\n}\n\nexport const ConsultantContext = createContext<ConsultantContextType | null>(null);\n\ninterface ConsultantContextProviderProps {\n  children: ReactNode;\n}\n\nconst ConsultantContextProvider = ({ children }: ConsultantContextProviderProps) => {\n  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || '';\n\n  const [dToken, setDToken] = useState<string>('');\n  const [appointments, setAppointments] = useState<Appointment[]>([]);\n  const [dashData, setDashData] = useState<DashData | false>(false);\n  const [profileData, setProfileData] = useState<ProfileData | false>(false);\n\n  // Initialize token from localStorage on client side\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('dToken');\n      if (token) {\n        setDToken(token);\n      }\n    }\n  }, []);\n\n  const getAppointments = async () => {\n    try {\n      const { data } = await axios.get(\n        backendUrl + '/api/consultant/appointments',\n        {\n          headers: {\n            dToken,\n          },\n        }\n      );\n      if (data.success) {\n        setAppointments(data.appointments);\n        console.log(data.appointments);\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      console.log(error);\n      toast.error(error.message);\n    }\n  };\n\n  const completeAppointment = async (appointmentId: string) => {\n    try {\n      const { data } = await axios.post(\n        backendUrl + '/api/consultant/complete-appointment',\n        { appointmentId },\n        {\n          headers: {\n            dToken,\n          },\n        }\n      );\n      if (data.success) {\n        toast.success(data.message);\n        getAppointments();\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      console.log(error);\n      toast.error(error.message);\n    }\n  };\n\n  const cancelAppointment = async (appointmentId: string) => {\n    try {\n      const { data } = await axios.post(\n        backendUrl + '/api/consultant/cancel-appointment',\n        { appointmentId },\n        {\n          headers: {\n            dToken,\n          },\n        }\n      );\n      if (data.success) {\n        toast.success(data.message);\n        getAppointments();\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      console.log(error);\n      toast.error(error.message);\n    }\n  };\n\n  const getDashData = async () => {\n    try {\n      const { data } = await axios.get(backendUrl + '/api/consultant/dashboard', {\n        headers: {\n          dToken,\n        },\n      });\n      if (data.success) {\n        setDashData(data.dashData);\n        console.log(data.dashData);\n      } else {\n        toast.error(data.message);\n      }\n    } catch (error: any) {\n      console.log(error);\n      toast.error(error.message);\n    }\n  };\n\n  const getProfileData = async () => {\n    try {\n      const { data } = await axios.get(backendUrl + '/api/consultant/profile', {\n        headers: {\n          dToken,\n        },\n      });\n      if (data.success) {\n        setProfileData(data.profileData);\n        console.log(data.profileData);\n      }\n    } catch (error: any) {\n      console.log(error);\n      toast.error(error.message);\n    }\n  };\n\n  const value: ConsultantContextType = {\n    dToken,\n    setDToken,\n    backendUrl,\n    appointments,\n    setAppointments,\n    getAppointments,\n    completeAppointment,\n    cancelAppointment,\n    dashData,\n    setDashData,\n    getDashData,\n    profileData,\n    setProfileData,\n    getProfileData,\n  };\n\n  return (\n    <ConsultantContext.Provider value={value}>\n      {children}\n    </ConsultantContext.Provider>\n  );\n};\n\nexport default ConsultantContextProvider;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAuEO,MAAM,kCAAoB,IAAA,sNAAa,EAA+B;AAM7E,MAAM,4BAA4B,CAAC,EAAE,QAAQ,EAAkC;IAC7E,MAAM,aAAa,QAAQ,GAAG,CAAC,uBAAuB,IAAI;IAE1D,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAS;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAgB,EAAE;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAmB;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAsB;IAEpE,oDAAoD;IACpD,IAAA,kNAAS,EAAC;QACR;;IAMF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,GAAG,CAC9B,aAAa,gCACb;gBACE,SAAS;oBACP;gBACF;YACF;YAEF,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,YAAY;gBACjC,QAAQ,GAAG,CAAC,KAAK,YAAY;YAC/B,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC;YACZ,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,IAAI,CAC/B,aAAa,wCACb;gBAAE;YAAc,GAChB;gBACE,SAAS;oBACP;gBACF;YACF;YAEF,IAAI,KAAK,OAAO,EAAE;gBAChB,4JAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B;YACF,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC;YACZ,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,IAAI,CAC/B,aAAa,sCACb;gBAAE;YAAc,GAChB;gBACE,SAAS;oBACP;gBACF;YACF;YAEF,IAAI,KAAK,OAAO,EAAE;gBAChB,4JAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B;YACF,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC;YACZ,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,GAAG,CAAC,aAAa,6BAA6B;gBACzE,SAAS;oBACP;gBACF;YACF;YACA,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,QAAQ;gBACzB,QAAQ,GAAG,CAAC,KAAK,QAAQ;YAC3B,OAAO;gBACL,4JAAK,CAAC,KAAK,CAAC,KAAK,OAAO;YAC1B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC;YACZ,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gJAAK,CAAC,GAAG,CAAC,aAAa,2BAA2B;gBACvE,SAAS;oBACP;gBACF;YACF;YACA,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe,KAAK,WAAW;gBAC/B,QAAQ,GAAG,CAAC,KAAK,WAAW;YAC9B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC;YACZ,4JAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,MAAM,QAA+B;QACnC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/context/AppContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, ReactNode } from 'react';\n\ninterface AppContextType {\n  calculateAge: (dob: string) => number;\n  slotDateFormat: (slotDate: string) => string;\n  currency: string;\n}\n\nexport const AppContext = createContext<AppContextType | null>(null);\n\ninterface AppContextProviderProps {\n  children: ReactNode;\n}\n\nconst AppContextProvider = ({ children }: AppContextProviderProps) => {\n  const currency = '$';\n\n  const calculateAge = (dob: string): number => {\n    const today = new Date();\n    const birthDate = new Date(dob);\n\n    let age = today.getFullYear() - birthDate.getFullYear();\n    return age;\n  };\n\n  const months = [\n    ' ',\n    'Jan',\n    'Feb',\n    'Mar',\n    'Apr',\n    'May',\n    'Jun',\n    'Jul',\n    'Aug',\n    'Sep',\n    'Oct',\n    'Nov',\n    'Dec',\n  ];\n\n  const slotDateFormat = (slotDate: string): string => {\n    const dateArray = slotDate.split('_');\n    return (\n      dateArray[0] + ' ' + months[Number(dateArray[1])] + ' ' + dateArray[2]\n    );\n  };\n\n  const value: AppContextType = {\n    calculateAge,\n    slotDateFormat,\n    currency,\n  };\n\n  return (\n    <AppContext.Provider value={value}>{children}</AppContext.Provider>\n  );\n};\n\nexport default AppContextProvider;\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AAUO,MAAM,2BAAa,IAAA,sNAAa,EAAwB;AAM/D,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAA2B;IAC/D,MAAM,WAAW;IAEjB,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAE3B,IAAI,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;QACrD,OAAO;IACT;IAEA,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,OACE,SAAS,CAAC,EAAE,GAAG,MAAM,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE,EAAE,GAAG,MAAM,SAAS,CAAC,EAAE;IAE1E;IAEA,MAAM,QAAwB;QAC5B;QACA;QACA;IACF;IAEA,qBACE,8OAAC,WAAW,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAExC;uCAEe", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NEXT/admin/src/components/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode } from 'react';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport AdminContextProvider from '@/context/AdminContext';\nimport ConsultantContextProvider from '@/context/ConsultantContext';\nimport AppContextProvider from '@/context/AppContext';\n\ninterface ProvidersProps {\n  children: ReactNode;\n}\n\nexport function Providers({ children }: ProvidersProps) {\n  return (\n    <AdminContextProvider>\n      <ConsultantContextProvider>\n        <AppContextProvider>\n          {children}\n          <ToastContainer />\n        </AppContextProvider>\n      </ConsultantContextProvider>\n    </AdminContextProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AACA;AACA;AAPA;;;;;;;AAaO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,qBACE,8OAAC,0IAAoB;kBACnB,cAAA,8OAAC,+IAAyB;sBACxB,cAAA,8OAAC,wIAAkB;;oBAChB;kCACD,8OAAC,qKAAc;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}]}