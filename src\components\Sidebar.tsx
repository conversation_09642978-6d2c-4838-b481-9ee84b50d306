'use client';

import React, { useContext } from 'react';
import { AdminContext } from '@/context/AdminContext';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { assets } from '@/assets/assets';
import { ConsultantContext } from '@/context/ConsultantContext';

const Sidebar: React.FC = () => {
  const adminContext = useContext(AdminContext);
  const consultantContext = useContext(ConsultantContext);
  const pathname = usePathname();

  if (!adminContext || !consultantContext) {
    return null;
  }

  const { aToken } = adminContext;
  const { dToken } = consultantContext;

  const isActive = (path: string) => pathname === path;

  const linkClass = (path: string) =>
    `flex items-center gap-3 py-3.5 px-3 md:px-9 md:min-w-72 cursor-pointer ${
      isActive(path) ? 'bg-[#F2F3FF] border-r-4 border-primary' : ''
    }`;

  return (
    <div className="min-h-screen bg-white border-r">
      {aToken && (
        <ul className="text-[#515151] mt-5">
          <Link href="/admin-dashboard" className={linkClass('/admin-dashboard')}>
            <img src={assets.home_icon} alt="Dashboard" />
            <p className="hidden md:block">Dashboard</p>
          </Link>
          <Link href="/all-appointments" className={linkClass('/all-appointments')}>
            <img src={assets.appointment_icon} alt="Appointments" />
            <p className="hidden md:block">Appointments</p>
          </Link>
          <Link href="/add-consultant" className={linkClass('/add-consultant')}>
            <img src={assets.add_icon} alt="Add Consultant" />
            <p className="hidden md:block">Add Consultant</p>
          </Link>
          <Link href="/consultant-list" className={linkClass('/consultant-list')}>
            <img src={assets.people_icon} alt="Consultant List" />
            <p>Consultant List</p>
          </Link>
        </ul>
      )}
      {dToken && (
        <ul className="text-[#515151] mt-5">
          <Link href="/consultant-dashboard" className={linkClass('/consultant-dashboard')}>
            <img src={assets.home_icon} alt="Dashboard" />
            <p className="hidden md:block">Dashboard</p>
          </Link>
          <Link href="/consultant-appointments" className={linkClass('/consultant-appointments')}>
            <img src={assets.appointment_icon} alt="Appointments" />
            <p className="hidden md:block">Appointments</p>
          </Link>
          <Link href="/consultant-profile" className={linkClass('/consultant-profile')}>
            <img src={assets.people_icon} alt="Profile" />
            <p className="hidden md:block">Profile</p>
          </Link>
        </ul>
      )}
    </div>
  );
};

export default Sidebar;
